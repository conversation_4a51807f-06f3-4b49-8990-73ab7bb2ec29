import React from 'react';
import {
  Text,
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
} from 'react-native';
import {RouteProp, useRoute, useNavigation} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {useQuery} from 'react-query';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {getStatusText, handleFormatDate} from '../../utils/fn';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import {useTheme} from 'react-native-paper';
import {share} from '../../requester-biz-service/apis/share-api';

type ShareDetailScreenRouteProp = RouteProp<
  HomeStackParamList,
  'ShareDetailScreen'
>;

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

const ShareDocumentDetailScreen = () => {
  const theme = useTheme();
  const route = useRoute<ShareDetailScreenRouteProp>();
  const navigation = useNavigation<NavigationProp>();
  const {detail} = route.params;

  // Use React Query to fetch detailed data
  const {
    data: shareDetailResponse,
    isLoading,
    error,
  } = useQuery(
    ['shareDetail', detail?.id],
    () => share.fetchShareById(detail?.id || ''),
    {
      enabled: !!detail?.id,
      onSuccess: response => {
        console.log('Share detail response:', response.data);
      },
      onError: err => {
        console.error('Error fetching share detail:', JSON.stringify(err));
      },
    },
  );

  // Use fetched data if available, otherwise fallback to route params
  const shareDetailData = shareDetailResponse?.data || detail;

  // Extract data from detail object
  const documentTitle = shareDetailData?.data?.[0]?.name || 'Tài liệu chia sẻ';
  const createdAt = shareDetailData?.createdAt || '';
  const progress = shareDetailData?.progress || 'new';

  const statusInfo = getStatusText(progress);

  const handleNavigateToMetadata = () => {
    navigation.navigate('ShareDetailMetadata', {
      id: detail?.id || '',
      detail: shareDetailData,
    });
  };

  const handleNavigateToDocsData = () => {
    navigation.navigate('ShareDetailDocsData', {
      id: detail?.id || '',
      detail: shareDetailData,
    });
  };

  const handleNavigateToProcessHistory = () => {
    navigation.navigate('ShareDetailProcessHistory', {
      id: detail?.id || '',
      detail: shareDetailData,
    });
  };

  const getSectionCardStyle = (): object => ({
    backgroundColor: theme.colors.surface,
    borderWidth: 0.5,
    borderColor: theme.colors.outline,
    shadowColor: theme.dark ? '#000' : theme.colors.outline,
    shadowOffset: {width: 0, height: theme.dark ? 1 : 2},
    shadowOpacity: theme.dark ? 0.3 : 0.12,
    shadowRadius: theme.dark ? 2 : 4,
    elevation: theme.dark ? 1 : 3,
  });

  // Show loading state
  // if (isLoading) {
  //   return (
  //     <View
  //       style={[styles.container, {backgroundColor: theme.colors.background}]}>
  //       <CCAppBar label="Chi tiết" isBack={true} />
  //       <View style={styles.loadingContainer}>
  //         <ActivityIndicator size="large" color={theme.colors.primary} />
  //         <Text
  //           style={[styles.loadingText, {color: theme.colors.onBackground}]}>
  //           Đang tải chi tiết...
  //         </Text>
  //       </View>
  //     </View>
  //   );
  // }

  // Show error state
  // if (error) {
  //   return (
  //     <View
  //       style={[styles.container, {backgroundColor: theme.colors.background}]}>
  //       <CCAppBar label="Chi tiết" isBack={true} />
  //       <View style={styles.errorContainer}>
  //         <Text style={[styles.errorText, {color: theme.colors.error}]}>
  //           {error instanceof Error ? error.message : 'Có lỗi xảy ra'}
  //         </Text>
  //       </View>
  //     </View>
  //   );
  // }

  return (
    <View
      style={[styles.container, {backgroundColor: theme.colors.background}]}>
      {/* Header */}
      <CCAppBar label="Chi tiết" isBack={true} />
      <ScrollView
        style={[styles.scrollView, {backgroundColor: theme.colors.background}]}
        showsVerticalScrollIndicator={false}>
        {/* Document Title Section */}
        <View
          style={[
            styles.titleSection,
            {backgroundColor: theme.colors.background},
          ]}>
          <Text
            style={[styles.documentTitle, {color: theme.colors.onBackground}]}>
            {documentTitle}
          </Text>
          <View
            style={[
              styles.statusBadge,
              {backgroundColor: statusInfo.background},
            ]}>
            <Text style={[styles.statusText, {color: statusInfo.color}]}>
              {statusInfo.label}
            </Text>
          </View>
        </View>
        {/* Submission Info */}
        <View
          style={[styles.infoSection, {backgroundColor: theme.colors.surface}]}>
          <Text
            style={[styles.infoText, {color: theme.colors.onSurfaceVariant}]}>
            Ngày nộp hồ sơ:{' '}
            {handleFormatDate({date: createdAt, format: 'hh:mm - DD/MM/YYYY'})}
          </Text>
          <Text
            style={[styles.infoText, {color: theme.colors.onSurfaceVariant}]}>
            {' '}
            (Cập nhật lần cuối:{' '}
            {handleFormatDate({
              date: shareDetailData?.updatedAt || createdAt,
              format: 'hh:mm - DD/MM/YYYY',
            })}
            ){' '}
          </Text>
        </View>
        {/* Document Details Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToMetadata}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Chi tiết hồ sơ
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Bao gồm các thông tin chi tiết như thông tin thủ tục, thông tin
            người yêu cầu, thông tin trả kết quả, v.v.
          </Text>
        </TouchableOpacity>
        {/* Document Components Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToDocsData}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Thành phần giấy tờ
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Những loại giấy tờ và tài liệu cần thiết đã được nộp kèm theo hồ sơ
          </Text>
        </TouchableOpacity>
        {/* Processing Status Section */}
        <TouchableOpacity
          style={[styles.section, getSectionCardStyle()]}
          onPress={handleNavigateToProcessHistory}>
          <View style={styles.sectionHeader}>
            <Text
              style={[styles.sectionTitle, {color: theme.colors.onSurface}]}>
              Quá trình xử lý
            </Text>
          </View>
          <Text
            style={[
              styles.sectionDescription,
              {color: theme.colors.onSurfaceVariant},
            ]}>
            Mô tả các giai đoạn mà một hồ sơ từ khi được tiếp nhận đến khi hoàn
            tất
          </Text>
        </TouchableOpacity>
      </ScrollView>

      {/* Action Button */}
      {/* <View
        style={[
          styles.buttonContainer,
          {backgroundColor: theme.colors.surface},
        ]}>
        <Button>Bổ sung hồ sơ</Button>
      </View> */}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
    paddingHorizontal: 16,
  },
  titleSection: {
    padding: 16,
    marginTop: 16,
    borderRadius: 8,
    marginBottom: 8,
  },
  documentTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
    lineHeight: 22,
  },
  statusBadge: {
    alignSelf: 'flex-start',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '500',
  },
  infoSection: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  infoText: {
    fontSize: 14,
    marginBottom: 4,
  },
  section: {
    padding: 16,
    borderRadius: 8,
    marginBottom: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    flex: 1,
  },
  sectionDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  buttonContainer: {
    padding: 16,
    borderTopWidth: 1,
  },
  actionButton: {
    paddingVertical: 4,
    borderRadius: 8,
    alignItems: 'center',
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    textAlign: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default ShareDocumentDetailScreen;
