import {FlatList, StyleSheet, Text, View, RefreshControl} from 'react-native';
import React, {useState, useCallback, useMemo} from 'react';
import {RouteProp, useNavigation, useRoute} from '@react-navigation/native';
import {NativeStackNavigationProp} from '@react-navigation/native-stack';
import {Chip, useTheme} from 'react-native-paper';
import {CCAppBar} from '../../components/QNH_CongDanComponents/HeaderComponent/CCAppBar';
import EmptyList from '../../components/EmptyList';
import ErrorBoundary from '../../components/QNH_CongDanComponents/ErrorBoundary';
import DocumentItemComponent from './components/DocumentItem';
import {useDocuments} from '../../hooks/useDocuments';
import type {DocumentItem} from '../../requester-biz-service/apis/documents-api';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import {HomeStackParamList} from '../../navigation/HomeNavigator';
import {useSubmitFlow} from '../../hooks/useSubmitFlow';
import {useConfigStore} from '@ac-mobile/common';

type TabType = 'ALL' | 'CURRENT_USER' | 'OTHER';

type NavigationProp = NativeStackNavigationProp<HomeStackParamList>;

interface TabConfig {
  key: TabType;
  label: string;
  uploadBy?: string; // Fixed: should be uploadBy, not uploadedBy
}

const TABS: TabConfig[] = [
  {key: 'ALL', label: 'Tất cả'},
  {key: 'CURRENT_USER', label: 'Giấy tờ tải lên', uploadBy: 'CURRENT_USER'},
  {key: 'OTHER', label: 'Giấy tờ được cấp', uploadBy: 'OTHER'},
];

type PDFViewerScreenRouteProp = RouteProp<
  HomeStackParamList,
  'DocumentsScreen'
>;

const DocumentsScreen = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<PDFViewerScreenRouteProp>();
  const {getConfig} = useConfigStore();
  const theme = useTheme();
  const editItemInArrayByCodeAndUri = useSubmitFlow(
    state => state.editItemInArrayByCodeAndUri,
  );
  // Safely extract code and name from route.params, fallback to undefined if not present
  const {code, name} = route.params || {};
  // Updated styles to use theme colors
  const styles = useMemo(
    () =>
      StyleSheet.create({
        container: {
          flex: 1,
          position: 'relative',
          backgroundColor: theme.colors.background,
        },
        tabContainer: {
          flexDirection: 'row',
          paddingHorizontal: 16,
          paddingVertical: 8,
          gap: 8,
          backgroundColor: theme.colors.surface,
        },
        tabButton: {
          flex: 1,
          paddingVertical: 12,
          paddingHorizontal: 16,
          marginRight: 8,
          backgroundColor: theme.colors.surfaceVariant,
          borderRadius: 20,
          alignItems: 'center',
        },
        activeTabButton: {
          backgroundColor: theme.colors.primary,
        },
        tabButtonText: {
          fontSize: 14,
          fontWeight: '500',
          color: theme.colors.onSurfaceVariant,
        },
        activeTabText: {
          color: theme.colors.onPrimary,
        },
        flatListStyle: {
          flex: 1,
          backgroundColor: theme.colors.background,
        },
        flatListContainer: {
          flexGrow: 1,
          paddingHorizontal: 16,
          paddingVertical: 12,
        },
        footerStyle: {
          margin: 16,
          justifyContent: 'center',
          alignItems: 'center',
        },
        footerTextStyle: {
          textAlign: 'center',
          color: theme.colors.onSurfaceVariant,
          fontSize: 14,
        },
        testPanel: {
          backgroundColor: theme.colors.surface,
          padding: 12,
          borderBottomWidth: 1,
          borderBottomColor: theme.colors.outline,
        },
        testPanelTitle: {
          fontSize: 14,
          fontWeight: '600',
          color: theme.colors.onSurface,
          marginBottom: 8,
        },
        testButtons: {
          flexDirection: 'row',
          gap: 8,
        },
        testButton: {
          backgroundColor: theme.colors.surfaceVariant,
          paddingVertical: 8,
          paddingHorizontal: 12,
          borderRadius: 6,
          alignItems: 'center',
          justifyContent: 'center',
        },
        testButtonText: {
          fontSize: 18,
          color: theme.colors.onSurface,
        },
        appBarTitle: {
          fontSize: 18,
          fontWeight: '600',
          color: theme.colors.onSurface,
        },
        chooseDocumentForName: {
          marginHorizontal: 16,
          marginTop: 8,
          marginBottom: 4,
        },
      }),
    [theme],
  );

  const [activeTab, setActiveTab] = useState<TabType>('ALL');
  const [uploadBy, setUploadBy] = useState<string | undefined>(undefined);

  // Use a single query instance that gets updated when tab changes
  const {
    data,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
    refetch,
    isRefetching,
  } = useDocuments({
    uploadBy: uploadBy,
    enabled: true,
  });
  const itemsArray = useSubmitFlow(state => state.itemsArray);
  const allDocs = data?.pages.flatMap(page => page.data.data.items) || [];
  const isEmpty = allDocs.length === 0;

  console.log({hasNextPage});

  // Upload type menu handlers
  const handleUploadTypeSelect = (
    uploadType:
      | 'camera'
      | 'image'
      | 'video'
      | 'videoCamera'
      | 'pdf'
      | 'document',
  ) => {
    navigation.navigate('DocumentUploadScreen', {autoPickType: uploadType});
  };

  const uploadMenuOptions = [
    // {
    //   title: 'Chụp ảnh',
    //   icon: 'camera',
    //   uploadType: 'camera' as const,
    // },
    // {
    //   title: 'Quay video',
    //   icon: 'record-rec',
    //   uploadType: 'videoCamera' as const,
    // },
    {
      title: 'Chọn ảnh',
      icon: 'file-image',
      uploadType: 'image' as const,
    },
    {
      title: 'Chọn video',
      icon: 'movie',
      uploadType: 'video' as const,
    },
    {
      title: 'Chọn tài liệu',
      icon: 'file-pdf-box',
      uploadType: 'pdf' as const,
    },
  ];

  // Get all documents from all pages (backend filtering applied)
  const items = useMemo(() => {
    if (!data?.pages) {
      return [];
    }

    return data.pages.flatMap((page: any) => {
      if (page?.data?.data?.items) {
        return page.data.data.items;
      } else if (page?.data?.items) {
        return page.data.items;
      }
      return [];
    });
  }, [data]);

  const handleDocumentPress = useCallback(
    (item: DocumentItem) => {
      if (code) {
        editItemInArrayByCodeAndUri(code, item.uri, {name: item.name});
        navigation.goBack();
      } else {
        navigation.navigate('DocumentDetailScreen', {uri: item.uri, item});
      }
    },
    [code, editItemInArrayByCodeAndUri, navigation],
  );

  const renderItem = useCallback(
    ({item}: {item: DocumentItem}) => {
      if (itemsArray?.find(doc => doc.uri === item.uri) && code) {
        // If the item is already in the itemsArray, skip rendering
        return null;
      }
      return (
        <DocumentItemComponent item={item} onPress={handleDocumentPress} />
      );
    },
    [code, handleDocumentPress, itemsArray],
  );

  const onEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  const onRefresh = useCallback(() => {
    refetch();
  }, [refetch]);

  const handleTabPress = useCallback((tabKey: TabType) => {
    const newTab = TABS.find(tab => tab.key === tabKey);
    setActiveTab(tabKey);
    setUploadBy(newTab?.uploadBy);
  }, []);

  const renderTabButton = useCallback(
    (tab: TabConfig) => {
      const isActive = activeTab === tab.key;
      return (
        <Chip
          key={tab.key}
          mode={isActive ? 'flat' : 'outlined'}
          onPress={() => handleTabPress(tab.key)}
          selectedColor={theme.colors.onPrimary}
          style={
            isActive
              ? {backgroundColor: theme.colors.primary}
              : {backgroundColor: theme.colors.surfaceVariant}
          }
          textStyle={
            isActive
              ? {color: theme.colors.onPrimary}
              : {color: theme.colors.onSurfaceVariant}
          }>
          {tab.label}
        </Chip>
      );
    },
    [
      activeTab,
      handleTabPress,
      theme.colors.onPrimary,
      theme.colors.primary,
      theme.colors.onSurfaceVariant,
      theme.colors.surfaceVariant,
    ],
  );

  const emptyList = useCallback(
    () => (
      <>
        {!isLoading && (
          <EmptyList
            title={'Không có tài liệu'}
            subtitle="Kéo xuống để tải lại"
          />
        )}
      </>
    ),
    [isLoading],
  );

  const footer = useCallback(
    () => (
      <View style={styles.footerStyle}>
        {isFetchingNextPage ? (
          <Text style={styles.footerTextStyle}>Đang tải</Text>
        ) : (
          <Text style={styles.footerTextStyle}>
            {isEmpty ? '' : hasNextPage ? 'Kéo để tải thêm' : 'Cuối trang'}
          </Text>
        )}
      </View>
    ),
    [
      isEmpty,
      isFetchingNextPage,
      hasNextPage,
      styles.footerStyle,
      styles.footerTextStyle,
    ],
  );
  console.log(
    getConfig('masterRemoteConfig')?.citizen &&
      getConfig('masterRemoteConfig')?.citizen?.uploadMenu,
    'uploadMenuConfig',
  );
  return (
    <ErrorBoundary name="DocumentsScreen" theme={theme}>
      <View style={styles.container}>
        {/* App Bar with CCAppBar Component */}
        <CCAppBar
          isBack
          label={name ? `Chọn tài liệu cho: ${name}` : 'Giấy tờ của tôi'}
          menuConfig={
            getConfig('masterRemoteConfig')?.citizen &&
            getConfig('masterRemoteConfig')?.citizen?.uploadMenu
              ? uploadMenuOptions.map(option => ({
                  icon: (
                    <Icon
                      name={option.icon}
                      size={22}
                      color={theme.colors.onSurface}
                    />
                  ),
                  text: option.title,
                  onPress: () => handleUploadTypeSelect(option.uploadType),
                }))
              : undefined
          }
        />
        <View style={styles.tabContainer}>{TABS.map(renderTabButton)}</View>
        <FlatList
          key={`documents-${uploadBy || 'all'}`}
          style={styles.flatListStyle}
          contentContainerStyle={styles.flatListContainer}
          refreshControl={
            <RefreshControl
              refreshing={isRefetching}
              onRefresh={onRefresh}
              tintColor={theme.colors.primary}
            />
          }
          data={items}
          renderItem={renderItem}
          keyExtractor={(item, index) =>
            `${uploadBy || 'all'}-${item.id}-${index}`
          }
          onEndReachedThreshold={0.1}
          onEndReached={onEndReached}
          ListEmptyComponent={emptyList}
          ListFooterComponent={footer}
          showsVerticalScrollIndicator={false}
          removeClippedSubviews={true}
          initialNumToRender={10}
          maxToRenderPerBatch={5}
          windowSize={5}
        />
      </View>
    </ErrorBoundary>
  );
};

export default DocumentsScreen;
