import {RequesterA<PERSON>} from '../requester-client';

type shareData = {
  metadata: Record<string, any>;
  shareType: 'DOCUMENT' | 'DOCUMENT_SET';
  data: {
    name: string;
    uri: string;
  }[];
};

type ShareDocument = {
  id: string;
  name: string;
  templateId: string;
  template: {
    id: string;
    name: string;
    code: string;
    size: number;
    tags: string[];
    isOld: boolean;
    status: number;
    syncAt: string | null;
    content: string;
    keyword: string;
    syncLog: any;
    version: number;
    content1: string | null;
    content2: string | null;
    content3: string | null;
    tenantId: string;
    createdAt: string;
    isDeleted: boolean;
    updatedAt: string;
    createdById: string;
    partnerCode: string | null;
    partnerData: any;
    categoryCode: string;
    categoryName: string;
    fileExampleUrl: string;
    data: {
      code: string;
      name: string;
      dataType: string;
      required: boolean;
      fileExampleUrl: string;
      fileTemplateUrl: string;
      partnerConfig?: any;
    }[];
  };
};

// This represents the detailed response from the API with additional nested data
type ShareDetailItem = {
  id: string;
  requestId: string;
  ownerId: string;
  requestResource: string;
  ownerResource: string;
  shareType: 'DOCUMENT' | 'DOCUMENT_SET';
  status: number;
  createdAt: string;
  acceptedAt: string | null;
  rejectedAt: string | null;
  expireAt: string | null;
  updatedAt: string;
  documentIds: string[];
  tenantId: string;
  templateIds: string[];
  data: {
    id: string;
    no: string;
    data: {
      code: string;
      name: string;
      value?: any;
      uri?: string;
      detail?: {
        uri: string;
        hash: string;
        docId: string;
        docName: string;
        docType: string;
        fileUrl: string;
        metadata: any;
        docOwnerId: string;
        docFileType: string;
        docOwnerType: string;
      };
      dataType: string;
      isAttachmentUri?: boolean;
      isExternalData?: boolean;
    }[];
    hash: string | null;
    name: string;
    size: number;
    type: string;
    isNew: boolean;
    shared: number;
    status: number;
    docType: number;
    keyword: string | null;
    ownerId: string;
    isSigned: boolean;
    issuerId: string;
    metadata: any;
    signedBy: any;
    template: ShareDocument['template'];
    tenantId: string;
    createdAt: string;
    syncByDvc: any;
    updatedAt: string;
    issuerInfo: any;
    templateId: string;
    contentType: string | null;
    requesterId: string;
    issuerOrigin: any;
    issuerOwnerId: string | null;
    signatureInfo: any;
    issuerServiceOwnerId: string | null;
  }[];
  requesterServiceId: string;
  owner: {
    fullName: string;
    identificationId: string;
  };
  templateCodes: string[];
  collector: {
    tenantId: string;
    requesterId: string;
    requestDevice: string;
  };
  forwardMetadata: {
    forwardData: any;
    identification: any;
  };
  progress: string;
  progressData: any;
  partnerApprovedAt: string | null;
  partnerSignedAt: string | null;
  keyword: string;
  shortId: string;
  partnerCode: string | null;
  partnerData: any;
  documents?: ShareDocument[];
  items: {
    id: string;
    ownerId: string;
    name: string;
    issuerId: string;
    type: string;
    no: string;
    size: number;
    contentType: string | null;
    status: number;
    docType: number;
    data: any[];
    shared: number;
    createdAt: string;
    updatedAt: string;
    requesterId: string;
    tenantId: string;
    templateId: string;
    issuerOwnerId: string | null;
    issuerServiceOwnerId: string | null;
    isNew: boolean;
    template: ShareDocument['template'];
    hash: string | null;
    isSigned: boolean;
    signatureInfo: any;
    metadata: any;
    keyword: string | null;
    signedBy: any;
    issuerOrigin: any;
    issuerInfo: any;
    syncByDvc: any;
    isFileDeleted: boolean;
    uri: string;
    progress: string;
    progressData: any;
    partnerApprovedAt: string | null;
    partnerSignedAt: string | null;
  }[];
  process: any[];
  files: {
    document: {
      fileUrl: string;
      metadata: any;
      docFileType: string;
      docName: string;
      docOwnerType: string;
      docType: string;
      uri: string;
      hash: string;
      docOwnerId: string;
      docId: string;
      additionalData: any[];
    };
    xmlFile: string;
    xmlExpiresIn: number;
    fileSize: number;
  }[];
};

type NewProfilesModel = {
  data: {
    kind: string;
    items: ShareDocument[];
    totalItems: number;
  };
};

const confirm = (data: {transactionId: string; otp: number}) => {
  return RequesterApi.post('/v1/shares/method/confirm', data);
};

const shareData = (data: shareData) => {
  console.log(data, 'data');
  return RequesterApi.post('/v1/shares', data);
};

const getShare = async (page: number = 1): Promise<NewProfilesModel> => {
  console.log('Fetching shares for page:', page);
  try {
    const response = await RequesterApi.get('/v1/shares', {
      params: {
        page,
        pageSize: 20,
        orderBy: 'createdAt',
        orderDir: 'desc',
      },
    });
    return response.data as NewProfilesModel;
  } catch (error) {
    console.error('Error:', error);
    throw new Error('Lỗi hệ thống, vui lòng thử lại sau');
  }
};

type ShareDetailResponse = {
  data: ShareDetailItem;
};

const fetchShareById = async (
  shareId: string,
): Promise<ShareDetailResponse> => {
  try {
    console.log('Fetching share by ID:', shareId);
    const response = await RequesterApi.get(`/v1/shares/${shareId}`);
    return response.data as ShareDetailResponse;
  } catch (error) {
    console.error('Error:', JSON.stringify(error));
    throw new Error('Lỗi hệ thống, vui lòng thử lại sau');
  }
};

export const share = {
  confirm,
  share: shareData,
  getShare,
  fetchShareById,
};
