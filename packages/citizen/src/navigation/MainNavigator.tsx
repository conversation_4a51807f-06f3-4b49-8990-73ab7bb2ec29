import React, {useContext, useEffect, useState} from 'react';
import {createNativeStackNavigator} from '@react-navigation/native-stack';
import TabsNavigator from './TabsNavigator';
import {useNavigation} from '@react-navigation/native';
import {View} from 'react-native';
import {TemplatesScreen} from '../screens/dashboard/QNHCongDan/CommonProcedures/TemplatesScreen';
import {CommonProcedureDetail} from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/CommonProcedureDetail';
import InformationCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/InformationCommonScreen';
import ProfileCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/ProfileCommonScreen';
import StepCommonScreen from '../screens/dashboard/QNHCongDan/CommonProcedures/CommonProcedureDetail/StepCommonScreen';
import {GlobalStateView} from '../components/AcView/GlobalStateView';
import {NotificationContext} from '@ac-mobile/common';
import {Dialog, Portal, Button, Text} from 'react-native-paper';
import {CommonProcedureSubmitScreen} from '../screens/dashboard/QNHCongDan/CommonProcedureSubmit/CommonProcedureSubmitScreen';
import DrawNumberBranchSelectScreen from '../screens/dashboard/QNHCongDan/DrawNumber/DrawNumberBranchSelectScreen';
import DrawNumberFieldSelectScreen from '../screens/dashboard/QNHCongDan/DrawNumber/DrawNumberFieldSelectScreen';
import DrawNumberConfirmationScreen from '../screens/dashboard/QNHCongDan/DrawNumber/DrawNumberConfirmationScreen';
import TermsScreen from '../screens/TermsScreen';
import TicketDetailScreen from '../screens/dashboard/QNHCongDan/TicketDetail/TicketDetailScreen';
import {StepOne} from '../screens/SubmitDocument/StepOne';
import {StepRoleChoose} from '../screens/SubmitDocument/StepRoleChoose';
import StepTwo from '../screens/SubmitDocument/StepTwo';
import StepThree from '../screens/SubmitDocument/StepThree';
import PDFViewerScreen from '../screens/SubmitDocument/PDFViewerScreen';
import DocumentsScreen from '../screens/document/DocumentsScreen';
import DocumentDetailScreen from '../screens/document/DocumentDetailScreen';
import StepFour from '../screens/SubmitDocument/StepFour';
import SuccessStep from '../screens/SubmitDocument/SuccessStep';

export type MainStackParamList = {
  Dashboard: undefined;
  CommonProcedure: undefined;
  CommonProcedureDetail: undefined;
  InformationCommonScreen: undefined;
  ProfileCommonScreen: undefined;
  StepCommonScreen: undefined;
  DrawNumber: undefined;
  CommonProcedureSubmit: undefined;
  DrawNumberBranchSelect: undefined;
  DrawNumberFieldSelect: undefined;
  DrawNumberConfirmation: undefined;
  TicketDetail: {ticketId: string};
  StepOne: undefined;
  StepRoleChoose: undefined;
  StepTwo: {userFormId: string};
  StepThree: undefined;
  StepFour: undefined;
  SuccessStep: {
    soBienNhan?: string;
    nopHoSoSoTienThanhToan?: string;
    nopHoSoSoTienCanThanhToanSau?: string;
    tongTien?: string;
    title?: string;
    isPayment?: boolean;
  };
  DocumentsScreen: {code?: string; name?: string};
  DocumentDetailScreen: {uri: string; item?: any};
  PDFViewerScreen: {pdfPath: string};
  Terms: undefined;
};

const Main = createNativeStackNavigator<MainStackParamList>();

const MainNavigator = () => {
  const navigation = useNavigation();
  const {notiData, setNotiData} = useContext(NotificationContext);
  const [dialogVisible, setDialogVisible] = useState(false);

  // Effect to show dialog and handle navigation on notiData
  useEffect(() => {
    if (
      notiData &&
      notiData.data &&
      notiData.data.notiData &&
      typeof notiData.data.notiData === 'string'
    ) {
      try {
        const parsed = JSON.parse(notiData.data.notiData);
        console.log(
          'Parsed notiData:',
          parsed,
          'parsed.miniappAliasparsed.miniappAlias',
          parsed.miniappAlias,
        );
        if (parsed.miniappAlias === 'CITIZEN') {
          setDialogVisible(true);
        }
      } catch (e) {
        // ignore parse error
      }
    }
  }, [notiData]);

  const handleDialogClose = () => {
    setDialogVisible(false);
    setNotiData(null);
  };

  const handleDialogConfirm = () => {
    setDialogVisible(false);
    setNotiData(null);
    // Use parsed.navigate or fallback to DrawNumberBranchSelect
    if (
      notiData &&
      notiData.data &&
      notiData.data.notiData &&
      typeof notiData.data.notiData === 'string'
    ) {
      try {
        const parsed = JSON.parse(notiData.data.notiData);
        const targetScreen = parsed.navigate;
        // @ts-ignore
        navigation.navigate(targetScreen);
      } catch (e) {
        // @ts-ignore
        navigation.navigate('DrawNumberBranchSelect');
      }
    }
  };

  return (
    <GlobalStateView>
      <View style={{flex: 1}}>
        <Portal>
          <Dialog visible={dialogVisible} onDismiss={handleDialogClose}>
            <Dialog.Title>
              {notiData?.notification?.title || 'Thông báo'}
            </Dialog.Title>
            <Dialog.Content>
              <Text style={{fontWeight: 'bold', marginBottom: 8}}>
                {notiData?.notification?.body || ''}
              </Text>
              <Text selectable style={{marginBottom: 8}}>
                {(() => {
                  if (
                    notiData &&
                    notiData.data &&
                    notiData.data.notiData &&
                    typeof notiData.data.notiData === 'string'
                  ) {
                    try {
                      const parsed = JSON.parse(notiData.data.notiData);
                      return parsed.body || '';
                    } catch (e) {
                      return notiData.data.notiData;
                    }
                  }
                  return '';
                })()}
              </Text>
              <Text style={{fontSize: 12, color: '#888'}}>
                Raw: {notiData?.data?.notiData || ''}
              </Text>
            </Dialog.Content>
            <Dialog.Actions>
              <Button onPress={handleDialogClose}>Đóng</Button>
              {notiData?.data?.notiData &&
                (() => {
                  try {
                    const parsed = JSON.parse(notiData.data.notiData);
                    return parsed.navigate ? (
                      <Button onPress={handleDialogConfirm}>Đi tới</Button>
                    ) : null;
                  } catch {
                    return null;
                  }
                })()}
            </Dialog.Actions>
          </Dialog>
        </Portal>
        <Main.Navigator
          screenOptions={{
            headerShown: false,
            headerTitleAlign: 'center',
            headerBackButtonDisplayMode: 'minimal',
          }}>
          <Main.Screen
            name="Dashboard"
            component={TabsNavigator}
            options={{headerShown: false, title: ''}}
          />
          <Main.Screen
            name="CommonProcedure"
            component={TemplatesScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="CommonProcedureDetail"
            component={CommonProcedureDetail}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="InformationCommonScreen"
            component={InformationCommonScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="ProfileCommonScreen"
            component={ProfileCommonScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepCommonScreen"
            component={StepCommonScreen}
            options={{
              headerShown: false,
            }}
          />

          <Main.Screen
            name="CommonProcedureSubmit"
            component={CommonProcedureSubmitScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DrawNumberBranchSelect"
            component={DrawNumberBranchSelectScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DrawNumberFieldSelect"
            component={DrawNumberFieldSelectScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DrawNumberConfirmation"
            component={DrawNumberConfirmationScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="TicketDetail"
            component={TicketDetailScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="Terms"
            component={TermsScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepOne"
            component={StepOne}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepRoleChoose"
            component={StepRoleChoose}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepTwo"
            component={StepTwo}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepThree"
            component={StepThree}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="StepFour"
            component={StepFour}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="SuccessStep"
            component={SuccessStep}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="PDFViewerScreen"
            component={PDFViewerScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DocumentsScreen"
            component={DocumentsScreen}
            options={{
              headerShown: false,
            }}
          />
          <Main.Screen
            name="DocumentDetailScreen"
            component={DocumentDetailScreen}
          />
        </Main.Navigator>
        {/* <SafeAreaView edges={['bottom']} style={{backgroundColor: 'red'}} /> */}
      </View>
    </GlobalStateView>
  );
};

export default MainNavigator;
