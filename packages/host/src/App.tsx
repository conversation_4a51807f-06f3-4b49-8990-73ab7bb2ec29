import remoteConfig from '@react-native-firebase/remote-config';
import React, {useEffect, useState} from 'react';
import RNBootSplash from 'react-native-bootsplash';
import {Federated} from '@callstack/repack/client';
import {NavigationContainer} from '@react-navigation/native';
import MainNavigator from './navigation/MainNavigator';
import SplashScreen from './components/SplashScreen';
import ErrorBoundary from './components/ErrorBoundary';
import GlobalNotification from './components/GlobalNotification';
import {useAuthStore, useConfigStore} from '@ac-mobile/common';
import {SafeAreaProvider} from 'react-native-safe-area-context';
import {addEventListener} from '@react-native-community/netinfo';
import {StyleSheet, View} from 'react-native';
import {Icon, PaperProvider, Text, useTheme} from 'react-native-paper';
import {NativeWindStyleSheet, useColorScheme} from 'nativewind';
import Toast from 'react-native-toast-message';
import {QueryClient, QueryClientProvider} from 'react-query';
import {GestureHandlerRootView} from 'react-native-gesture-handler';
import 'react-native-reanimated';
import {lightTheme, darkTheme} from './theme';
import CitizenMiniAppScreen from './screens/CitizenMiniAppScreen';
import DeviceInfo from 'react-native-device-info';

declare global {
  var RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS: boolean | undefined;
}
globalThis.RNFB_SILENCE_MODULAR_DEPRECATION_WARNINGS = true;

const AuthNavigator = React.lazy(() =>
  Federated.importModule('auth', './AuthNavigator'),
);

// This import is no longer needed as we're using the AuthNavigator

const AuthNavigatorContainer = () => {
  React.useEffect(() => {
    RNBootSplash.hide({fade: true});
  }, []);
  return <AuthNavigator />;
};

const Main = ({children}: any) => {
  const theme = useTheme();
  const queryClient = new QueryClient();
  const [isConnected, setIsConnected] = useState<boolean | undefined | null>(
    true,
  );

  useEffect(() => {
    const unsubscribe = addEventListener(state => {
      setIsConnected(state.isConnected);
    });
    return () => {
      unsubscribe();
    };
  }, []);

  return (
    <View
      style={{
        flex: 1,
      }}>
      {!isConnected && (
        <View
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            flexDirection: 'row',
            gap: 4,
          }}>
          <Icon size={12} color={theme.colors.error} source="wifi-alert" />
          <Text style={{fontSize: 12, color: theme.colors.error}}>
            Vui lòng kiểm tra kết nối mạng!
          </Text>
        </View>
      )}
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    </View>
  );
};

const App = () => {
  const {colorScheme} = useColorScheme();
  const {user, accessToken} = useAuthStore();
  const {setOneConfig, getConfig} = useConfigStore();

  NativeWindStyleSheet.setColorScheme(colorScheme || 'light');
  useEffect(() => {
    (async () => {
      try {
        // await remoteConfig().setConfigSettings({
        //   fetchTimeMillis: 0,
        //   minimumFetchIntervalMillis: 0, // Add this for testing
        // });
        const activated = await remoteConfig().fetchAndActivate();
        if (activated) {
          console.log('Remote config successfully fetched and activated!');
        } else {
          console.log(
            'Remote config fetched, but not activated (likely no new changes).',
          );
        }
        setOneConfig(
          'appStoreNotificationServiceBackendURL',
          remoteConfig()
            .getValue('appStoreNotificationServiceBackendURL')
            .asString() || '',
        );
        setOneConfig(
          'notificationEnable',
          remoteConfig().getValue('notificationEnable').asBoolean(),
        );
        setOneConfig(
          'masterRemoteConfig',
          JSON.parse(
            await remoteConfig().getValue('masterRemoteConfig').asString(),
          ),
        );
        console.log('API Config set in store');
      } catch (e) {
        console.error('Failed to fetch remote config:', JSON.stringify(e), e);
      }
    })();
  }, [setOneConfig]);
  let bundleId = DeviceInfo.getBundleId();
  console.log(getConfig('loginMode'), 'loginMode');

  let loginMode = getConfig('loginMode');

  useEffect(() => {
    if (
      bundleId === 'vn.gov.quangninh.dichvucong' &&
      getConfig('loginMode') !== 'citizen'
    ) {
      setOneConfig('loginMode', 'citizen');
    } else if (
      bundleId === 'vn.gov.quangninh.dichvucong.congchuc' &&
      getConfig('loginMode') !== 'officer'
    ) {
      setOneConfig('loginMode', 'officer');
    }
  }, [bundleId, setOneConfig, getConfig]);

  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  // Wait for config to be set before rendering
  const hasConfig = !!getConfig('appStoreNotificationServiceBackendURL');

  if (loginMode == null || !hasConfig) {
    return (
      <PaperProvider theme={theme}>
        <GestureHandlerRootView style={{flex: 1}}>
          <SafeAreaProvider>
            <SplashScreen />
          </SafeAreaProvider>
        </GestureHandlerRootView>
      </PaperProvider>
    );
  }

  return (
    <GestureHandlerRootView style={{flex: 1}}>
      <SafeAreaProvider>
        <PaperProvider theme={theme}>
          <GlobalNotification>
            <View style={styles.container}>
              <ErrorBoundary name="AuthProvider">
                <React.Suspense fallback={<SplashScreen />}>
                  {loginMode === 'citizen' ? (
                    accessToken ? (
                      <NavigationContainer
                        onReady={() => RNBootSplash.hide({fade: true})}>
                        <Main>
                          <React.Suspense fallback={<SplashScreen />}>
                            <CitizenMiniAppScreen />
                          </React.Suspense>
                        </Main>
                      </NavigationContainer>
                    ) : (
                      <React.Suspense fallback={<SplashScreen />}>
                        <AuthNavigatorContainer />
                      </React.Suspense>
                    )
                  ) : loginMode === 'officer' ? (
                    <>
                      {(!user ||
                        (typeof user === 'object' &&
                          Object.keys(user).length === 0)) && (
                        <React.Suspense fallback={<SplashScreen />}>
                          <AuthNavigatorContainer />
                        </React.Suspense>
                      )}
                      {user && (
                        <NavigationContainer
                          onReady={() => RNBootSplash.hide({fade: true})}>
                          <Main>
                            <MainNavigator />
                          </Main>
                        </NavigationContainer>
                      )}
                    </>
                  ) : null}
                </React.Suspense>
                <Toast />
              </ErrorBoundary>
            </View>
          </GlobalNotification>
        </PaperProvider>
      </SafeAreaProvider>
    </GestureHandlerRootView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});

export default App;
