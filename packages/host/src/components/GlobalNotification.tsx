import React, {useState, useCallback} from 'react';
import NotificationProvider from './NotificationProvider';
import {NotificationContext} from '@ac-mobile/common';

export const GlobalNotification: React.FC<{children: React.ReactNode}> = ({
  children,
}) => {
  const [notiData, setNotiData] = useState<any>(null);

  const showNotiDialog = useCallback((noti: any) => {
    if (noti?.data?.notiData) {
      try {
        JSON.parse(noti.data.notiData);
      } catch (e) {
        // fallback: notiData is not JSON
      }
    }
    console.log('Notification received:', noti);
    setNotiData(noti);
  }, []);

  return (
    <NotificationProvider onNoti={showNotiDialog}>
      <NotificationContext.Provider value={{notiData, setNotiData}}>
        {children}
      </NotificationContext.Provider>
    </NotificationProvider>
  );
};

export default GlobalNotification;
